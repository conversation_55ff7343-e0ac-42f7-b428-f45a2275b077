param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("powerup", "powerdown", "mount", "unmount")]
    [string]$Action,

    [Parameter(Mandatory=$true)]
    [int[]]$IPs,

    [string]$ImagePath = "//**************/ISOShare/ubuntu-24.04-live-server-amd64.iso"
)

# iDRAC IP base
$ipBase = "************"

# iDRAC credentials
$username = "blade"
$password = "changeme1"

# Build full IP list
$ipList = $IPs | ForEach-Object { "$ipBase$_" }

foreach ($ip in $ipList) {
    switch ($Action) {
        "powerup" {
            Write-Host "Powering up $ip..."
            racadm --nocertwarn -r $ip -u $username -p $password serveraction powerup
        }

        "powerdown" {
            Write-Host "Powering down $ip..."
            racadm --nocertwarn -r $ip -u $username -p $password serveraction powerdown
        }

        "mount" {
            Write-Host "Mounting ISO and booting from it on $ip..."
            racadm --nocertwarn -r $ip -u $username -p $password remoteimage -c -u smbuser -p calvin -l $ImagePath
            racadm --nocertwarn -r $ip -u $username -p $password remoteimage -s
            racadm --nocertwarn -r $ip -u $username -p $password set iDRAC.VirtualMedia.BootOnce 1
            racadm --nocertwarn -r $ip -u $username -p $password set iDRAC.ServerBoot.FirstBootDevice VCD-DVD
            racadm --nocertwarn -r $ip -u $username -p $password serveraction powercycle
        }

        "unmount" {
            Write-Host "Unmounting ISO on $ip..."
            racadm --nocertwarn -r $ip -u $username -p $password remoteimage -d
            racadm --nocertwarn -r $ip -u $username -p $password remoteimage -s
        }

        default {
            Write-Error "Invalid action specified: $Action"
        }
    }
}
