Connecting to server: 172.20.176.119 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.119
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, <PERSON>, <PERSON>, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 60 minutes for test to complete.
Test will complete after Thu Aug 14 22:16:28 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.123 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.123
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 60 minutes for test to complete.
Test will complete after Thu Aug 14 22:16:30 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.124 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.124
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 60 minutes for test to complete.
Test will complete after Thu Aug 14 22:16:32 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.126 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.126
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 60 minutes for test to complete.
Test will complete after Thu Aug 14 22:16:34 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
 Connecting to server: 172.20.176.127 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.127
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:55:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:55:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:55:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:55:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Thu Aug 14 23:59:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Thu Aug 14 23:59:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1097 minutes for test to complete.
Test will complete after Fri Aug 15 18:06:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1106 minutes for test to complete.
Test will complete after Fri Aug 15 18:15:49 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1080 minutes for test to complete.
Test will complete after Fri Aug 15 17:49:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1189 minutes for test to complete.
Test will complete after Fri Aug 15 19:38:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1124 minutes for test to complete.
Test will complete after Fri Aug 15 18:33:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1108 minutes for test to complete.
Test will complete after Fri Aug 15 18:17:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1147 minutes for test to complete.
Test will complete after Fri Aug 15 18:56:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1175 minutes for test to complete.
Test will complete after Fri Aug 15 19:24:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1113 minutes for test to complete.
Test will complete after Fri Aug 15 18:22:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1104 minutes for test to complete.
Test will complete after Fri Aug 15 18:13:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1112 minutes for test to complete.
Test will complete after Fri Aug 15 18:21:50 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1134 minutes for test to complete.
Test will complete after Fri Aug 15 18:43:51 2025 UTC
Use smartctl -X to abort test.
 
 ----------------------------------------------------------------

 Connecting to server: 172.20.176.128 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.128
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:00:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:00:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1132 minutes for test to complete.
Test will complete after Fri Aug 15 18:42:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1144 minutes for test to complete.
Test will complete after Fri Aug 15 18:54:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1160 minutes for test to complete.
Test will complete after Fri Aug 15 19:10:16 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1070 minutes for test to complete.
Test will complete after Fri Aug 15 17:40:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1092 minutes for test to complete.
Test will complete after Fri Aug 15 18:02:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1186 minutes for test to complete.
Test will complete after Fri Aug 15 19:36:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1127 minutes for test to complete.
Test will complete after Fri Aug 15 18:37:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1165 minutes for test to complete.
Test will complete after Fri Aug 15 19:15:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1116 minutes for test to complete.
Test will complete after Fri Aug 15 18:26:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1186 minutes for test to complete.
Test will complete after Fri Aug 15 19:36:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1175 minutes for test to complete.
Test will complete after Fri Aug 15 19:25:17 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1185 minutes for test to complete.
Test will complete after Fri Aug 15 19:35:17 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 

Connecting to server: ************** (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on **************
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Fri Aug 15 00:03:04 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Fri Aug 15 00:03:04 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Fri Aug 15 00:03:04 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:07:04 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:07:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Fri Aug 15 00:03:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1090 minutes for test to complete.
Test will complete after Fri Aug 15 18:07:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1128 minutes for test to complete.
Test will complete after Fri Aug 15 18:45:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1114 minutes for test to complete.
Test will complete after Fri Aug 15 18:31:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1106 minutes for test to complete.
Test will complete after Fri Aug 15 18:23:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1072 minutes for test to complete.
Test will complete after Fri Aug 15 17:49:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1138 minutes for test to complete.
Test will complete after Fri Aug 15 18:55:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1069 minutes for test to complete.
Test will complete after Fri Aug 15 17:46:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1098 minutes for test to complete.
Test will complete after Fri Aug 15 18:15:05 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1089 minutes for test to complete.
Test will complete after Fri Aug 15 18:06:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1169 minutes for test to complete.
Test will complete after Fri Aug 15 19:26:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1046 minutes for test to complete.
Test will complete after Fri Aug 15 17:23:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1130 minutes for test to complete.
Test will complete after Fri Aug 15 18:47:06 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.130 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.130
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:00:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:00:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1124 minutes for test to complete.
Test will complete after Fri Aug 15 18:34:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1080 minutes for test to complete.
Test will complete after Fri Aug 15 17:50:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1070 minutes for test to complete.
Test will complete after Fri Aug 15 17:40:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1086 minutes for test to complete.
Test will complete after Fri Aug 15 17:56:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1062 minutes for test to complete.
Test will complete after Fri Aug 15 17:32:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1136 minutes for test to complete.
Test will complete after Fri Aug 15 18:46:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1168 minutes for test to complete.
Test will complete after Fri Aug 15 19:18:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1139 minutes for test to complete.
Test will complete after Fri Aug 15 18:49:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1112 minutes for test to complete.
Test will complete after Fri Aug 15 18:22:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1188 minutes for test to complete.
Test will complete after Fri Aug 15 19:38:44 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1086 minutes for test to complete.
Test will complete after Fri Aug 15 17:56:44 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1201 minutes for test to complete.
Test will complete after Fri Aug 15 19:51:44 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.131 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.131
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:00:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:00:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:56:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1058 minutes for test to complete.
Test will complete after Fri Aug 15 17:28:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1090 minutes for test to complete.
Test will complete after Fri Aug 15 18:00:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1148 minutes for test to complete.
Test will complete after Fri Aug 15 18:58:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1125 minutes for test to complete.
Test will complete after Fri Aug 15 18:35:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1236 minutes for test to complete.
Test will complete after Fri Aug 15 20:26:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1013 minutes for test to complete.
Test will complete after Fri Aug 15 16:43:56 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1204 minutes for test to complete.
Test will complete after Fri Aug 15 19:54:57 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1129 minutes for test to complete.
Test will complete after Fri Aug 15 18:39:57 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1085 minutes for test to complete.
Test will complete after Fri Aug 15 17:55:57 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1116 minutes for test to complete.
Test will complete after Fri Aug 15 18:26:57 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1124 minutes for test to complete.
Test will complete after Fri Aug 15 18:34:57 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1158 minutes for test to complete.
Test will complete after Fri Aug 15 19:08:57 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.132 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.132
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:06 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1202 minutes for test to complete.
Test will complete after Fri Aug 15 19:53:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1189 minutes for test to complete.
Test will complete after Fri Aug 15 19:40:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1219 minutes for test to complete.
Test will complete after Fri Aug 15 20:10:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1071 minutes for test to complete.
Test will complete after Fri Aug 15 17:42:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1088 minutes for test to complete.
Test will complete after Fri Aug 15 17:59:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1143 minutes for test to complete.
Test will complete after Fri Aug 15 18:54:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1190 minutes for test to complete.
Test will complete after Fri Aug 15 19:41:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1192 minutes for test to complete.
Test will complete after Fri Aug 15 19:43:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1113 minutes for test to complete.
Test will complete after Fri Aug 15 18:24:07 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1137 minutes for test to complete.
Test will complete after Fri Aug 15 18:48:08 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1193 minutes for test to complete.
Test will complete after Fri Aug 15 19:44:08 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1186 minutes for test to complete.
Test will complete after Fri Aug 15 19:37:08 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.133 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.133
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1062 minutes for test to complete.
Test will complete after Fri Aug 15 17:33:19 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1134 minutes for test to complete.
Test will complete after Fri Aug 15 18:45:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1170 minutes for test to complete.
Test will complete after Fri Aug 15 19:21:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1139 minutes for test to complete.
Test will complete after Fri Aug 15 18:50:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1139 minutes for test to complete.
Test will complete after Fri Aug 15 18:50:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1094 minutes for test to complete.
Test will complete after Fri Aug 15 18:05:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1110 minutes for test to complete.
Test will complete after Fri Aug 15 18:21:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1173 minutes for test to complete.
Test will complete after Fri Aug 15 19:24:20 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1190 minutes for test to complete.
Test will complete after Fri Aug 15 19:41:21 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:21 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1103 minutes for test to complete.
Test will complete after Fri Aug 15 18:14:21 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1177 minutes for test to complete.
Test will complete after Fri Aug 15 19:28:21 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1086 minutes for test to complete.
Test will complete after Fri Aug 15 17:57:21 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.134 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.134
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1071 minutes for test to complete.
Test will complete after Fri Aug 15 17:42:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1101 minutes for test to complete.
Test will complete after Fri Aug 15 18:12:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1210 minutes for test to complete.
Test will complete after Fri Aug 15 20:01:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1113 minutes for test to complete.
Test will complete after Fri Aug 15 18:24:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1095 minutes for test to complete.
Test will complete after Fri Aug 15 18:06:32 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1158 minutes for test to complete.
Test will complete after Fri Aug 15 19:09:33 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1058 minutes for test to complete.
Test will complete after Fri Aug 15 17:29:33 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1118 minutes for test to complete.
Test will complete after Fri Aug 15 18:29:33 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1079 minutes for test to complete.
Test will complete after Fri Aug 15 17:50:33 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1216 minutes for test to complete.
Test will complete after Fri Aug 15 20:07:33 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1118 minutes for test to complete.
Test will complete after Fri Aug 15 18:29:33 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-64-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1072 minutes for test to complete.
Test will complete after Fri Aug 15 17:43:33 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.135 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.135
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:42 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1225 minutes for test to complete.
Test will complete after Fri Aug 15 20:16:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1111 minutes for test to complete.
Test will complete after Fri Aug 15 18:22:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1163 minutes for test to complete.
Test will complete after Fri Aug 15 19:14:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1111 minutes for test to complete.
Test will complete after Fri Aug 15 18:22:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1112 minutes for test to complete.
Test will complete after Fri Aug 15 18:23:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1190 minutes for test to complete.
Test will complete after Fri Aug 15 19:41:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1137 minutes for test to complete.
Test will complete after Fri Aug 15 18:48:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1102 minutes for test to complete.
Test will complete after Fri Aug 15 18:13:43 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1102 minutes for test to complete.
Test will complete after Fri Aug 15 18:13:44 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1095 minutes for test to complete.
Test will complete after Fri Aug 15 18:06:44 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1130 minutes for test to complete.
Test will complete after Fri Aug 15 18:41:44 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-71-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1108 minutes for test to complete.
Test will complete after Fri Aug 15 18:19:44 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
Connecting to server: 172.20.176.136 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/run_disk_checks.sh on 172.20.176.136
 
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 6 minutes for test to complete.
Test will complete after Thu Aug 14 23:57:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 10 minutes for test to complete.
Test will complete after Fri Aug 15 00:01:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1058 minutes for test to complete.
Test will complete after Fri Aug 15 17:29:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1107 minutes for test to complete.
Test will complete after Fri Aug 15 18:18:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1073 minutes for test to complete.
Test will complete after Fri Aug 15 17:44:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1183 minutes for test to complete.
Test will complete after Fri Aug 15 19:34:46 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1156 minutes for test to complete.
Test will complete after Fri Aug 15 19:07:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1109 minutes for test to complete.
Test will complete after Fri Aug 15 18:20:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1173 minutes for test to complete.
Test will complete after Fri Aug 15 19:24:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1127 minutes for test to complete.
Test will complete after Fri Aug 15 18:38:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1052 minutes for test to complete.
Test will complete after Fri Aug 15 17:23:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1106 minutes for test to complete.
Test will complete after Fri Aug 15 18:17:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1070 minutes for test to complete.
Test will complete after Fri Aug 15 17:41:47 2025 UTC
Use smartctl -X to abort test.
smartctl 7.4 2023-08-01 r5530 [x86_64-linux-6.8.0-31-generic] (local build)
Copyright (C) 2002-23, Bruce Allen, Christian Franke, www.smartmontools.org

=== START OF OFFLINE IMMEDIATE AND SELF-TEST SECTION ===
Sending command: "Execute SMART Extended self-test routine immediately in off-line mode".
Drive command "Execute SMART Extended self-test routine immediately in off-line mode" successful.
Testing has begun.
Please wait 1163 minutes for test to complete.
Test will complete after Fri Aug 15 19:14:47 2025 UTC
Use smartctl -X to abort test.
 
 ---------------------------------------------------------------- 
 
/Users/<USER>/Documents/CODE/OpenSearch/run_script_for_servers.sh 2220 "29" /Users/<USER>/Documents/CODE/OpenSearch/install_smartctl.sh
Connecting to server: ************** (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/install_smartctl.sh on **************
 

WARNING: apt does not have a stable CLI interface. Use with caution in scripts.

Get:1 file:/cdrom noble InRelease
Ign:1 file:/cdrom noble InRelease
Get:2 file:/cdrom noble Release
Err:2 file:/cdrom noble Release
  File not found - /cdrom/dists/noble/Release (2: No such file or directory)
Get:3 http://security.ubuntu.com/ubuntu noble-security InRelease [126 kB]
Hit:4 http://us.archive.ubuntu.com/ubuntu noble InRelease
Get:5 http://us.archive.ubuntu.com/ubuntu noble-updates InRelease [126 kB]
Get:6 http://us.archive.ubuntu.com/ubuntu noble-backports InRelease [126 kB]
Get:7 http://us.archive.ubuntu.com/ubuntu noble-updates/main amd64 Packages [1,322 kB]
Get:8 http://us.archive.ubuntu.com/ubuntu noble-updates/main Translation-en [264 kB]
Get:9 http://us.archive.ubuntu.com/ubuntu noble-updates/main amd64 Components [164 kB]
Get:10 http://us.archive.ubuntu.com/ubuntu noble-updates/restricted amd64 Packages [1,668 kB]
Get:11 http://us.archive.ubuntu.com/ubuntu noble-updates/restricted Translation-en [367 kB]
Get:12 http://us.archive.ubuntu.com/ubuntu noble-updates/restricted amd64 Components [212 B]
Get:13 http://us.archive.ubuntu.com/ubuntu noble-updates/universe amd64 Packages [1,120 kB]
Get:14 http://us.archive.ubuntu.com/ubuntu noble-updates/universe amd64 Components [377 kB]
Get:15 http://us.archive.ubuntu.com/ubuntu noble-updates/multiverse amd64 Components [940 B]
Get:16 http://us.archive.ubuntu.com/ubuntu noble-backports/main amd64 Components [7,076 B]
Get:17 http://us.archive.ubuntu.com/ubuntu noble-backports/restricted amd64 Components [216 B]
Get:18 http://us.archive.ubuntu.com/ubuntu noble-backports/universe amd64 Packages [30.2 kB]
Get:19 http://us.archive.ubuntu.com/ubuntu noble-backports/universe Translation-en [17.4 kB]
Get:20 http://us.archive.ubuntu.com/ubuntu noble-backports/universe amd64 Components [19.2 kB]
Get:21 http://us.archive.ubuntu.com/ubuntu noble-backports/multiverse amd64 Components [212 B]
Get:22 http://security.ubuntu.com/ubuntu noble-security/main amd64 Packages [1,058 kB]
Get:23 http://security.ubuntu.com/ubuntu noble-security/main Translation-en [184 kB]
Get:24 http://security.ubuntu.com/ubuntu noble-security/main amd64 Components [21.5 kB]
Get:25 http://security.ubuntu.com/ubuntu noble-security/restricted amd64 Packages [1,569 kB]
Get:26 http://security.ubuntu.com/ubuntu noble-security/restricted Translation-en [345 kB]
Get:27 http://security.ubuntu.com/ubuntu noble-security/restricted amd64 Components [212 B]
Get:28 http://security.ubuntu.com/ubuntu noble-security/universe amd64 Packages [879 kB]
Get:29 http://security.ubuntu.com/ubuntu noble-security/universe Translation-en [194 kB]
Get:30 http://security.ubuntu.com/ubuntu noble-security/universe amd64 Components [52.3 kB]
Get:31 http://security.ubuntu.com/ubuntu noble-security/multiverse amd64 Components [212 B]
Reading package lists...
E: The repository 'file:/cdrom noble Release' no longer has a Release file.
Get:1 file:/cdrom noble InRelease
Ign:1 file:/cdrom noble InRelease
Get:2 file:/cdrom noble Release
Err:2 file:/cdrom noble Release
  File not found - /cdrom/dists/noble/Release (2: No such file or directory)
Hit:3 http://security.ubuntu.com/ubuntu noble-security InRelease
Hit:4 http://us.archive.ubuntu.com/ubuntu noble InRelease
Hit:5 http://us.archive.ubuntu.com/ubuntu noble-updates InRelease
Hit:6 http://us.archive.ubuntu.com/ubuntu noble-backports InRelease
Reading package lists...
E: The repository 'file:/cdrom noble Release' no longer has a Release file.

WARNING: apt does not have a stable CLI interface. Use with caution in scripts.

Reading package lists...
Building dependency tree...
Reading state information...
Suggested packages:
  gsmartcontrol smart-notifier mailx | mailutils
The following NEW packages will be installed:
  smartmontools
0 upgraded, 1 newly installed, 0 to remove and 286 not upgraded.
Need to get 643 kB of archives.
After this operation, 2,211 kB of additional disk space will be used.
Get:1 http://us.archive.ubuntu.com/ubuntu noble/main amd64 smartmontools amd64 7.4-2build1 [643 kB]
debconf: unable to initialize frontend: Dialog
debconf: (Dialog frontend will not work on a dumb terminal, an emacs shell buffer, or without a controlling terminal.)
debconf: falling back to frontend: Readline
debconf: unable to initialize frontend: Readline
debconf: (This frontend requires a controlling tty.)
debconf: falling back to frontend: Teletype
dpkg-preconfigure: unable to re-open stdin: 
Fetched 643 kB in 0s (4,147 kB/s)
Selecting previously unselected package smartmontools.
(Reading database ... 84463 files and directories currently installed.)
Preparing to unpack .../smartmontools_7.4-2build1_amd64.deb ...
Unpacking smartmontools (7.4-2build1) ...
Setting up smartmontools (7.4-2build1) ...
/var/lib/smartmontools/drivedb/drivedb.h 7.3/5528 newly installed (NOT VERIFIED)
Created symlink /etc/systemd/system/multi-user.target.wants/smartd.service → /usr/lib/systemd/system/smartd.service.
Created symlink /etc/systemd/system/smartd.service → /usr/lib/systemd/system/smartmontools.service.
Created symlink /etc/systemd/system/multi-user.target.wants/smartmontools.service → /usr/lib/systemd/system/smartmontools.service.
smartd.service is a disabled or a static unit, not starting it.
Processing triggers for man-db (2.12.0-4build2) ...

Running kernel seems to be up-to-date.

The processor microcode seems to be up-to-date.

No services need to be restarted.

No containers need to be restarted.

No user sessions are running outdated binaries.

No VM guests are running outdated hypervisor (qemu) binaries on this host. 
 