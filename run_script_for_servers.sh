#!/bin/bash

# Usage:
# ./script.sh <ssh_port> [ip_suffixes] <remote_script> [username]
# Examples:
#   ./script.sh 23 "27 28 29" setup_remote.sh
#   ./script.sh 23 setup_remote.sh
#   ./script.sh 23 "27 28 29" setup_remote.sh myuser
#   ./script.sh 23 setup_remote.sh myuser

# Require at least SSH port
if [ $# -lt 1 ]; then
    echo "Usage: $0 <ssh_port> [ip_suffixes] <remote_script> [username]"
    exit 1
fi

SSH_PORT="$1"
BASE_IP="************"
USERNAME="josteele" # default

DEFAULT_SUFFIXES=(19 23 24 26 27 28 29 30 31 32 33 34 35 36)

if [ -f "$2" ]; then
    # Case: ./script.sh 2220 setup_remote.sh [username]
    IP_SUFFIXES=("${DEFAULT_SUFFIXES[@]}")
    REMOTE_SCRIPT="$2"
    [ -n "$3" ] && USERNAME="$3"
else
    # Case: ./script.sh 2220 "27 28" setup_remote.sh [username]
    if [ -n "$2" ]; then
        IFS=', ' read -r -a IP_SUFFIXES <<< "$2"
    else
        IP_SUFFIXES=("${DEFAULT_SUFFIXES[@]}")
    fi
    REMOTE_SCRIPT="$3"
    [ -n "$4" ] && USERNAME="$4"
fi

for suffix in "${IP_SUFFIXES[@]}"; do
    ip="${BASE_IP}${suffix}"
    echo "Connecting to server: $ip (port $SSH_PORT) as user: $USERNAME"

    if [ -n "$REMOTE_SCRIPT" ] && [ -f "$REMOTE_SCRIPT" ]; then
        echo "Uploading and running $REMOTE_SCRIPT on $ip"
        echo " "
        scp -P "$SSH_PORT" "$REMOTE_SCRIPT" "$USERNAME@$ip:/tmp/remote_script.sh" > /dev/null
        ssh -p "$SSH_PORT" "$USERNAME@$ip" "chmod +x /tmp/remote_script.sh && /tmp/remote_script.sh $ip"
        echo " ---------------------------------------------------------------- "
        echo " "
    else
        echo "No remote script provided. Skipping execution on $ip."
    fi
done
