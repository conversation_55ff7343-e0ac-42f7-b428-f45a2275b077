#!/bin/bash

# Usage: ./script.sh <ssh_port> [ip_suffixes]
# Example with list: ./script.sh 2220 "27 28 29"
# Example with default list: ./script.sh 2220

# Check if at least the port is provided
if [ $# -lt 1 ]; then
    echo "Usage: $0 <ssh_port> [ip_suffixes]"
    echo "Example with list: $0 2220 \"27 28 29\""
    echo "Example with default list: $0 2220"
    exit 1
fi

SSH_PORT="$1"
BASE_IP="************"
USERNAME="josteele"

# Predefined list of 14 suffixes for default case
DEFAULT_SUFFIXES=(19 23 24 26 27 28 29 30 31 32 33 34 35 36)

# If a second argument is given, split into array; otherwise use default list
if [ -n "$2" ]; then
    IFS=', ' read -r -a IP_SUFFIXES <<< "$2"
else
    IP_SUFFIXES=("${DEFAULT_SUFFIXES[@]}")
fi

# Loop through suffixes
for suffix in "${IP_SUFFIXES[@]}"; do
    ip="${BASE_IP}${suffix}"
    echo "Setting up ansible sudoer on server: $ip (port $SSH_PORT)"

    # Run the setup command over SSH
    ssh -p "$SSH_PORT" "$USERNAME@$ip" "SUDO_FILE='/etc/sudoers.d/ansible' && \
        echo 'ansible ALL=(root) NOPASSWD:ALL' | sudo tee \"\$SUDO_FILE\" && \
        sudo chmod 0440 \"\$SUDO_FILE\""

    echo "Ansible sudoer setup completed on $ip"
done
