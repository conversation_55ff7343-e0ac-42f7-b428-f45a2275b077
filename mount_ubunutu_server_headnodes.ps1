# List of iDRAC IPs
$ipList = @(
    "**************",
    "**************",
    "**************",
    "**************"
)

$username = "blade"
$password = "changeme1"

foreach ($ip in $ipList) {
    Write-Host "Mounting Ubunut to $ip..."
racadm --nocertwarn -r $ip -u $username -p $password remoteimage -c -u smbuser -p calvin -l //**************/ISOShare/ubuntu-24.04-live-server-amd64.iso
racadm --nocertwarn -r $ip -u $username -p $password remoteimage -s
}