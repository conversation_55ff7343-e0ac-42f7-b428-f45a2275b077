# List of iDRAC IPs
$ipList = @(
    "**************",
    "**************",
    "**************"
)

$username = "blade"
$password = "changeme1"

foreach ($ip in $ipList) {
    Write-Host "Booting Ubunut on $ip..."
    racadm --nocertwarn -r $ip -u $username -p $password remoteimage -s
    racadm set iDRAC.VirtualMedia.BootOnce 1
    racadm set iDRAC.ServerBoot.FirstBootDevice VCD-DVD
    racadm serveraction powercycle
}