disks=($(sudo fdisk -l | grep -E "447.13|118|9.1|1.45" | awk '{print $2}' | sed 's/.$//' | grep -v '^[0-9]*$' | grep -E "^/dev/s"))
counter=0
for disk in "${disks[@]}"; do
    if [ "$counter" -lt 4 ]; then
        result=$(sudo smartctl -a "$disk"  -d "megaraid,0" | awk ' /^[0-9]/ && /Pending|Reallocated|Uncorrectable/ && $NF != 0') 
    else
        result=$(sudo smartctl -a "$disk" | awk ' /^[0-9]/ && /Pending|Reallocated|Uncorrectable/ && $NF != 0')
    fi
    if [ -n "$result" ]; then
        echo Server: "$1"
        echo Disk: "$disk"
        echo ""
        echo Disk Info:
        echo ""
        sudo smartctl -i "$disk"
        echo ""
        echo "$result"
        echo ""
    fi  
    counter=$((counter +1))
done