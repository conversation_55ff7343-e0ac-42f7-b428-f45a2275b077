Command: sudo fdisk -l

Connecting to server: 172.20.176.119 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.119
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/sda: 1.45 TiB, 1599070011392 bytes, 3123183616 sectors
Disk model: PERC H330 Mini  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Disklabel type: gpt
Disk identifier: E8A9AB53-9FCA-4CDE-B572-867C3B0ADEE3

Device       Start        End    Sectors  Size Type
/dev/sda1     2048       4095       2048    1M BIOS boot
/dev/sda2     4096    4198399    4194304    2G Linux filesystem
/dev/sda3  4198400 3123181567 3118983168  1.5T Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 100 GiB, 107374182400 bytes, 209715200 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.123 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.123
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/sda: 1.45 TiB, 1599070011392 bytes, 3123183616 sectors
Disk model: PERC H330 Mini  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Disklabel type: gpt
Disk identifier: 861F1556-1BF5-457F-B412-74C4E4CBC6FA

Device       Start        End    Sectors  Size Type
/dev/sda1     2048       4095       2048    1M BIOS boot
/dev/sda2     4096    4198399    4194304    2G Linux filesystem
/dev/sda3  4198400 3123181567 3118983168  1.5T Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 100 GiB, 107374182400 bytes, 209715200 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.124 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.124
list_all_disks.sh                                                                                                                                                 100%   13     0.4KB/s   00:00    
Disk /dev/sda: 1.45 TiB, 1599070011392 bytes, 3123183616 sectors
Disk model: PERC H330 Mini  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Disklabel type: gpt
Disk identifier: 5CE49361-5F26-45FA-9886-7715B477F7E3

Device       Start        End    Sectors  Size Type
/dev/sda1     2048       4095       2048    1M BIOS boot
/dev/sda2     4096    4198399    4194304    2G Linux filesystem
/dev/sda3  4198400 3123181567 3118983168  1.5T Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 100 GiB, 107374182400 bytes, 209715200 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.126 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.126
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/sda: 1.45 TiB, 1599070011392 bytes, 3123183616 sectors
Disk model: PERC H330 Mini  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Disklabel type: gpt
Disk identifier: 33A8F851-4CEB-4A13-802E-7FF1CE107724

Device       Start        End    Sectors  Size Type
/dev/sda1     2048       4095       2048    1M BIOS boot
/dev/sda2     4096    4198399    4194304    2G Linux filesystem
/dev/sda3  4198400 3123181567 3118983168  1.5T Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 100 GiB, 107374182400 bytes, 209715200 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes

Connecting to server: 172.20.176.127 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.127
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 84DBCAB3-3748-414F-8B21-519CD267F2FD

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: AA9572D4-D559-4044-B2F2-83F59E6DA908

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.128 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.128
list_all_disks.sh                                                                                                                                                 100%   13     0.5KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: D3C46FEF-BA13-49ED-A9D3-EAE12AFBB3D6

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 352B696D-1536-4ADE-BE9D-D866091C52F0

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.129 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.129
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: ADD18612-3ECB-4E5F-BFF6-5ACC81109F7C

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 58C54A3A-BD0B-41BA-B670-3EA9C0ED0ACD

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Connecting to server: 172.20.176.130 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.130
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: B0728948-68C7-42CF-BBC0-A206021DCEA1

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 4877A4AE-600A-4DA5-B843-D6F91CFF5305

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.131 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.131
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: FBBC77C8-FD73-413F-A9E8-26E21BA4879F

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 47B22E9D-1FF8-420B-B3E0-A860B9E95D0F

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.132 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.132
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: F0BA0BD3-429B-490B-8AED-C58F95F514F7

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 09E95E8B-580A-4D5C-994D-65ADF428D9A1

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.133 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.133
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 58CAED13-E29F-428D-8BA0-0EC972119017

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdb: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdf: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: D19E2805-6C20-450A-A090-F88A972375ED

Device       Start       End   Sectors   Size Type
/dev/sdr1     2048   2203647   2201600     1G EFI System
/dev/sdr2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdr3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Connecting to server: 172.20.176.134 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.134
list_all_disks.sh                                                                                                                                                 100%   13     0.4KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 8A7DBEC1-13C7-42E0-B2EB-29C4E84F6511

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 92B14577-E5C6-40BB-9622-C55147FD088A

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.135 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.135
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: DB5A2D17-D625-40E2-ADD6-6E963C9ABEAD

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 022CBE31-D82E-4C3F-B1D7-11F5C5FD0381

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes
Connecting to server: 172.20.176.136 (port 2220) as user: josteele
Uploading and running /Users/<USER>/Documents/CODE/OpenSearch/list_all_disks.sh on 172.20.176.136
list_all_disks.sh                                                                                                                                                 100%   13     0.3KB/s   00:00    
Disk /dev/nvme0n1: 27.25 GiB, 29260513280 bytes, 57149440 sectors
Disk model: INTEL MEMPEK1W032GA                     
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: 8DECBAFA-9DA8-4357-B27A-4CC89B8CFC0D

Device           Start      End  Sectors  Size Type
/dev/nvme0n1p1    2048  2203647  2201600    1G EFI System
/dev/nvme0n1p2 2203648 57147391 54943744 26.2G Linux filesystem


Disk /dev/sdb: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdc: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sda: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdd: 447.13 GiB, 480103981056 bytes, 937703088 sectors
Disk model: Micron_5200_MTFD
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sde: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes


Disk /dev/sdf: 118 GiB, 126701535232 bytes, 247463936 sectors
Disk model: SuperMicro SSD  
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes
Disklabel type: gpt
Disk identifier: C6B01EBD-6710-4672-84FC-3B6491E25B3A

Device       Start       End   Sectors   Size Type
/dev/sdf1     2048   2203647   2201600     1G EFI System
/dev/sdf2  2203648   6397951   4194304     2G Linux filesystem
/dev/sdf3  6397952 247461887 241063936 114.9G Linux filesystem


Disk /dev/sdg: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdh: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdi: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdk: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdj: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdl: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdn: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdm: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdq: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdo: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdp: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/sdr: 9.1 TiB, 10000831348736 bytes, 19532873728 sectors
Disk model: HGST HUH721010AL
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 4096 bytes
I/O size (minimum/optimal): 4096 bytes / 4096 bytes


Disk /dev/mapper/ubuntu--vg-ubuntu--lv: 57.47 GiB, 61710794752 bytes, 120528896 sectors
Units: sectors of 1 * 512 = 512 bytes
Sector size (logical/physical): 512 bytes / 512 bytes
I/O size (minimum/optimal): 512 bytes / 512 bytes