#!/bin/bash

STATIC_IP_BASE="172.20.176."         # Change
STATIC_IP_END="/24"

read -p "Enter the last three digits for server: " STATIC_IP_LAST_THREE
STATIC_IP="${STATIC_IP_BASE}${STATIC_IP_LAST_THREE}${STATIC_IP_END}"
echo "Configuring $STATIC_IP"

MONITORING_SUBNET="************/24" 
GATEWAY_IP="**************"
SSH_PORT="2220"

NET_IFACE=$(ip -o link show | awk -F': ' '/state UP/ && $2 != "lo" {print $2; exit}')

NETPLAN_CONFIG=$(cat <<EOF
network:
  version: 2
  ethernets:
    $NET_IFACE:
      dhcp4: false
      addresses:
        - $STATIC_IP
      routes:
        - to: default
          via: $GATEWAY_IP
EOF
)

declare -A USERS
USERS["rclsa"]="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDhO6bNOtgvtwrw9kmDm85ahkP0jwUkTp2wb4RW0xywnYsnd7pYwQ/8uYjWHo92V5SpPW55CHSj2J+bbBaRXi/gO4m7Y5hnggp4gTTbAm71JR0UcceL3L3YjQRxbMd0ZMHPKpI2OGLN1vvHcBUC+wQH4VEJFnxR71fSdy1kTKa2RMA6Ncbt1yJ1fuvuZMPVFGqHBTdLVliuumhzxTB7VjseK5iZmXuIyYnWuugmQDSWWm4mGKoR7AGJHK12jSVE9jgRZDnUkj99who+BSuFBMQ1f2mO7aqXMxRUKyC9MlAjL1rpZ2vnK5Of5xRVsfty973uV6/nVcB9I94QsgvNvpJ4NiQp4Oxx+UaKPaQCCNLD1xmD0C5vhDfLzsI4wT/0eJh3cATohI4bZYqQ8Ro+9MkDf9YTLGDdx8Q90XiI4aIx3BcM0VT/ibTcmB2LSmcXpIW5XUG07hpX34m/YgNmkB9HmAJllp6OevnHdvRridfEdEm7Hxe9nEUpFthQGkIMeEPR5o0q6/jAI+CZBG97sGUsywLJ5pmb0QczYXJNV87WeCLvpB4ABq1ZKNg/73t6cKMvvAmRUPLZXCoyJd7ADGKkYcw5d9mXZcNn+WPYOJKDCXmrINW/Aq6rwh3zlAxvLWfUTMMV8NjF4rBApUMELyYlcNFjWRJtpRsR4qxBfHRT0w== <EMAIL>"
USERS["bach"]="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC7cCvMbASV/atpDWZ4QHuZbPvgEcqKfWrBpkiXJoPpIW452qYrplhv1vr+9wpu0tetcyl94GvyFA4ACvttp5UQ7kh8dVktdldSSM8O93xSz+euFsAHMLmKtbr37rw8hyPiutV4NjC1EmxM+2GrCeaKqeaUISWL8FE2NsOvf5hbFpenpGp4RdVpO1A2VymPIoPHtGmI0a0Y/tGt7Bc7CA4l97/zo0FnuSrWRXOyIR4ndZAMvuFmixBLJg9vAya4ebP2a7hRmE7NhNwTMtvYjkp+S08jw7GrJaoO6Yj+UgZ6ofrwXvXAK1aRuVgUU7SbXybS2D7lJnuW5r0JSZutkb/+CPZirOtDx+9POXSiuA/jQOqJYQmnhqNRDQHJsWdac5BuDlMp0CuTgTyrTLuw0+7TpD4W56C2UxLS+k2709ZDJsJSwCNTYGbNtT+zsQEJq0ISKH76UhAbm6jZ3dzRxnOMWju0U/Uzuai8f/9GIr1mkP4/tarIKnIbWGGZcLKZRO+36XzvAV95nBJ6fdqMqqjemVyRbturzuXtz5Al1mxi2sUIJe1W3QeD1XBHgWaas2SizTWw3l/vLjvsOTkGB5JKj4mtVKneeLrIsyUMrKZ8XPqXw/kvlipoeKocYKEB/2rsDT14xT2le8aOPjYE1iBRBa1yEaLSl/3B2AhJDfjBuQ== bach@Noronha"
USERS["leight"]="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDANdK8zNjFDysm02OdgAv0fZAb9dnl4JjCcjXmwQu2/AtnMgzxsv0/1NO7o0sfUKWWXer2aCI1BrVPYbashL+6sTlZp66mdgPxka5RUvAPcLji0FroJPRp7/VFES88dLipbuHFLr3hVzM6JfVsxo1qqF7RS38iB9e/Oavt4K9ZcQNzarmjYNpegUM7LJAUCdbIb/RWvsQM5HHpY3JW00roPOojkGQgYU0RASCD6tkKeHY7vUDYUrq6j3j0BjDfek5QcZvddZPAqwy7NO09b8zqDBAfEYkaadWGTWqh3rito3aBo1KK7e1BypWesA1xSZN27MLI8bXCSEJUF5Ae0bBQB7/PDTrJ+zSJydQQ9nKSBDmoBoXekvmnIYxa7L/uVkXzPowVToV1MylZPfWTK/iEEbjRS6QOxxkVfgGKmToSDYmzTjb+LkMEFwsBDjVTa81OxtfreA9mGNN/MkqEjkMW6Yt8gaj9N1fZxf9sWjW6r9wPo0v4Ap7kyhFESYR2KzU= <EMAIL>"
USERS["akman"]="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDPmJLapKrCGf7e6EZCyjCj41/WczYrJYSvxrqGhIays4pNfj50D+WtGDyoEfMAuIdgSibnNk88lKAXbm1mm/BnlM+kfTp9sLimPRHY8NlZ+PfOJO5zWReBWcv8TAgCXMFr2LEpd8Q76U5BwXvd+kcbgV9/FLqL1qT5Yp/qOV2zg2yJ92QZk2CcXxFBRoRrKYd9dd3GvjwY5027rFsxVDADxzftQvnkD1Nxm15/dJ+FegDNRsoXqvNn8PQpVTOy/4s7ylcmzkCMHVltFGchW2+AfPFCeTtzM1zsYEYxIB+gzgynTrFfVtgdq/2nYVtVvQBI4YtBTEgXznGwlko1p2g+IY7KWXKbi+wtYOdiK1YyTSiBwSR5vadollqgfzcR/oybGVPdJ+raU/LLBZ/sOr4bhdaSUav4PBQNYDEhPOeZkdYwAuAUYGJil0D2t7AjiGS5mayenGI0Yov+ecVskRdAGr9eeg3ZD0tByXB32EXeEyCZLNXrxK5VLCttK7vuvjtsCJnwtZYDip7Cu4qDi6opFffDcLkjU7+3rc9AG3ZFVmYY7f+bO0tuK51E6vlXmnNfBGbIZ3W+q7m6iUcRLkiQeb9XfCw7nfrX9AP5SX9WJoIFSsEsnHKvBz1fdF9+C3acHPoHcK+ObNpmwJ/oRUX2D7RQ8RpPcP1z9oo4lno3qw== <EMAIL>"
USERS["josteele"]="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDGaPZOZGsXGII1QE5cfnhb/C17yHBnP2VhB3yN+7qzB/jPgo/ex0F+sNpMfdqKs6sMLZiyv5avzx5xfdLYV8lQD/sa3pCSMRkKXUDwLRTob80Rlwz9lY68jaJL3vBmbNnpElxOdBcvRgez4AMjCabmWQlctT2RKdFU3AD7Tqe2iB8fN6/G2GeezrsEXPdGqblWwcqa1GrnrItk4YmhUXvmrkr8ylSmvgKekOVfXjRM0bbw1gTQ8YS1WGcrnQXJoOGu0/+6DB6wwO0BJVhIE+YarCZEK0+M1wGURTOxarkDfq452j8LLAXcSG/vggfJkWdNhKkjeUl/XXAUUlqRvsbGzD4QFLcoka5gLPrvD8pEw3RLRFXSQ44WbD2IqIpZ1tO976i9E/cvTJ3vmEYYCnwPJ1wPQiXXKvCO4eE2F5DLLSsYaA9W7Jsoi9YSZrFn4oAhlf8n+n4ZsYSz/Mq8aJcIJHEWJ7nAOuXy38cOxiknwhN8EcM59/XLNAUljHrV5kc= <EMAIL>"

set -e

for USER in "${!USERS[@]}"; do

    PUBKEY="${USERS[$USER]}"

    echo "Configuring user $USER"

    # Create user if it doesn't exist
    if id "$USER"; then
        echo "User '$USER' already exists. Skipping."
    else
        sudo adduser --disabled-password --gecos "" "$USER"
        echo "Created user '$USER'"
    fi

    # SSH key
    SSH_DIR="/home/<USER>/.ssh"
    AUTH_KEYS="$SSH_DIR/authorized_keys"

    sudo mkdir -p "$SSH_DIR"
    if sudo grep -qF "$PUBKEY" "$AUTH_KEYS"; then
        echo "SSH key already present for $USER"
    else
        echo "$PUBKEY" | sudo tee -a "$AUTH_KEYS"
        echo "Added SSH key for $USER"
    fi
    sudo chmod 700 "$SSH_DIR"
    sudo chmod 600 "$AUTH_KEYS"
    sudo chown -R "$USER:$USER" "$SSH_DIR"

    # Sudoers config
    SUDO_FILE="/etc/sudoers.d/$USER"
    if [[ -f "$SUDO_FILE" ]]; then
        echo "Sudoers file already exists for $USER"
    else
        echo "$USER ALL=(root) NOPASSWD:ALL" | sudo tee "$SUDO_FILE"
        sudo chmod 0440 "$SUDO_FILE"
        echo "Sudoers file created for $USER"
    fi

    # Disable password login
    if sudo passwd -S "$USER" | grep -q "L"; then
        echo "User '$USER' password already locked"
    else
        sudo passwd -l "$USER"
        echo "Locked password for $USER"
    fi
done

# SSHD
SSHD_FILE="/etc/ssh/sshd_config.d/net2.conf"
if [[ -f "$SSHD_FILE" ]]; then
    echo "SSHD already configured at $SSHD_FILE"
else
    echo "Configuring SSHD"
    sudo mkdir -p "$(dirname "$SSHD_FILE")"
    sudo tee "$SSHD_FILE" > /dev/null <<EOF
Port $SSH_PORT
PasswordAuthentication no
PubkeyAuthentication yes
AllowAgentForwarding yes
PermitRootLogin no
EOF
    sudo systemctl restart ssh
    echo "SSHD restarted on port $SSH_PORT"
fi

# ---- NETWORK SETUP SECTION ----

NETPLAN_FILE="/etc/netplan/01-netcfg.yaml"

# Check if netplan configuration already exists
if [[ -f "$NETPLAN_FILE" ]]; then
    echo "Network configuration already exists at $NETPLAN_FILE"
    echo "Current configuration:"
    echo "----------------------------------------"
    sudo cat "$NETPLAN_FILE"
    echo "----------------------------------------"
    echo ""
    echo "Script variables:"
    echo "  Interface: $NET_IFACE"
    echo "  Static IP: $STATIC_IP"
    echo ""
    
    while true; do
        read -p "Do you want to overwrite the existing network configuration? (y/n): " yn
        case $yn in
            [Yy]* ) 
                echo "Overwriting network configuration..."
                echo "Configuring static IP on $NET_IFACE..."
                sudo rm -rf "$NETPLAN_FILE"
                echo "$NETPLAN_CONFIG" | sudo tee "$NETPLAN_FILE" > /dev/null
                echo "New configuration:"
                sudo cat "$NETPLAN_FILE"
                break
                ;;
            [Nn]* ) 
                echo "Skipping network configuration..."
                break
                ;;
            * ) 
                echo "Please answer yes (y) or no (n)."
                ;;
        esac
    done
else
    echo "Configuring static IP on $NET_IFACE..."
    echo "$NETPLAN_CONFIG" | sudo tee "$NETPLAN_FILE" > /dev/null
    echo "New configuration:"
    sudo cat "$NETPLAN_FILE"
fi

sudo chmod 700 "$NETPLAN_FILE"

echo "Applying Netplan..."
sudo netplan apply

# Confirm connectivity before proceeding
# if ping -c 2 archive.ubuntu.com &>/dev/null; then
#     echo "Network connected."
# else
#     echo "Network is unreachable. Please change IP/gateway settings before continuing."
# fi


# UFW
# UFW_CHECK=$(sudo ufw status | grep "$SSH_PORT/tcp" | grep "$MONITORING_SUBNET" || true)
# if [[ -z "$UFW_CHECK" ]]; then
echo "Configuring UFW rules"
sudo ufw default deny incoming
sudo ufw allow from "$MONITORING_SUBNET" to any port "$SSH_PORT" proto tcp
sudo ufw default allow outgoing
sudo ufw --force enable
echo "Firewall configured"
# else
#     echo "UFW already allows port $SSH_PORT from $MONITORING_SUBNET"
# fi

# FAIL2BAN
if ! command -v fail2ban-client; then
    echo "Installing fail2ban"
    sudo apt install fail2ban -y
fi

JAIL_FILE="/etc/fail2ban/jail.local"
if [[ -f "$JAIL_FILE" && "$(grep -c "\[sshd\]" "$JAIL_FILE")" -gt 0 ]]; then
    echo "fail2ban already configured"
else
    echo "Setting up fail2ban"
    sudo mkdir -p "$(dirname "$JAIL_FILE")"
    sudo tee "$JAIL_FILE" > /dev/null <<EOF
[sshd]
enabled = true
port = $SSH_PORT
filter = sshd
logpath = /var/log/auth.log
maxretry = 5
EOF
    sudo systemctl enable fail2ban
    sudo systemctl restart fail2ban
    echo "fail2ban active on port $SSH_PORT"
fi

echo -e "\n Setup complete \n"
echo "TEST: ssh -p $SSH_PORT <user>@<host>"
